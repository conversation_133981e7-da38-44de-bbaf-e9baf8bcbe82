# ITR-1 (Sahaj) Income Tax Return Application

A comprehensive React application for filing ITR-1 (Sahaj) Income Tax Returns for Assessment Year 2024-25.

## Features

- **Multi-step Form**: Complete ITR-1 form with 7 sections
- **Auto-save**: Automatic saving of form data
- **Data Import/Export**: Load and save form data as JSON
- **Real-time Validation**: Form validation with error messages
- **Responsive Design**: Works on desktop and mobile devices
- **Tax Calculations**: Automatic tax computation
- **Progress Tracking**: Visual progress indicator

## Form Sections

1. **Personal Information**: Name, PAN, Aadhaar, Address, Contact details
2. **Filing Status**: Return filing section, tax regime options
3. **Income Details**: Salary, House Property, Other Sources
4. **Deductions**: Chapter VI-A deductions (80C, 80D, etc.)
5. **Tax Computation**: Tax calculation and rebates
6. **Bank Details**: Refund bank account information
7. **Verification**: Declaration and signature

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd itr1-new
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm test` - Launches the test runner
- `npm run build` - Builds the app for production
- `npm run eject` - Ejects from Create React App

## Dependencies

- React 18.2.0
- React DOM 18.2.0
- React Scripts 5.0.1
- Web Vitals 2.1.4

## Development Dependencies

- TypeScript 4.9.0
- React Types
- Node Types

## Project Structure

```
itr1-new/
├── public/
│   ├── index.html
│   └── manifest.json
├── src/
│   ├── components/
│   │   └── ITR1Application.tsx
│   ├── index.js
│   ├── index.css
│   └── reportWebVitals.js
├── package.json
├── .gitignore
└── README.md
```

## Usage

1. Fill out each section of the form step by step
2. Use the navigation buttons to move between sections
3. Save your progress using the "Save Draft" button
4. Load previously saved data using "Load Data"
5. Download the completed form as JSON using "Download JSON"

## Validation Rules

- PAN: Must follow the format **********
- Aadhaar: 12-digit number
- Email: Valid email format
- Mobile: 10-digit number starting with 1-9
- Pincode: 6-digit number
- IFSC: Valid IFSC code format

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Disclaimer

This application is for educational and demonstration purposes. Please consult with a qualified tax professional for actual tax filing. 
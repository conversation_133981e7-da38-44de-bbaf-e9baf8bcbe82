# ITR-1 (Sahaj) Income Tax Return Application

A comprehensive React application for filing ITR-1 (Sahaj) Income Tax Returns for Assessment Year 2024-25.

## Features

- **Multi-step Form**: Complete ITR-1 form with 7 sections
- **Auto-save**: Automatic saving of form data
- **Data Import/Export**: Load and save form data as JSON
- **Real-time Validation**: Form validation with error messages
- **Responsive Design**: Works on desktop and mobile devices
- **Tax Calculations**: Automatic tax computation
- **Progress Tracking**: Visual progress indicator

## Form Sections

1. **Personal Information**: Name, PAN, Aadhaar, Address, Contact details
2. **Filing Status**: Return filing section, tax regime options
3. **Income Details**: Salary, House Property, Other Sources
4. **Deductions**: Chapter VI-A deductions (80C, 80D, etc.)
5. **Tax Computation**: Tax calculation and rebates
6. **Bank Details**: Refund bank account information
7. **Verification**: Declaration and signature

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd itr1-new
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm test` - Launches the test runner
- `npm run build` - Builds the app for production
- `npm run eject` - Ejects from Create React App

## Dependencies

- React 18.2.0
- React DOM 18.2.0
- React Scripts 5.0.1
- Web Vitals 2.1.4

## Development Dependencies

- TypeScript 4.9.0
- React Types
- Node Types

## Project Structure

```
itr1-new/
├── public/
│   ├── index.html
│   └── manifest.json
├── src/
│   ├── components/
│   │   ├── common/           # Reusable UI components
│   │   │   ├── Button.jsx
│   │   │   ├── Input.jsx
│   │   │   ├── Select.jsx
│   │   │   ├── Notification.jsx
│   │   │   └── ProgressBar.jsx
│   │   └── ITR1Application.tsx
│   ├── constants/
│   │   └── itr-constants.ts  # Application constants
│   ├── context/
│   │   └── ITRContext.js     # Global state management
│   ├── hooks/
│   │   ├── useLocalStorage.js
│   │   ├── useNotifications.js
│   │   └── useFormValidation.js
│   ├── types/
│   │   └── itr-types.ts      # TypeScript definitions
│   ├── utils/
│   │   ├── tax-calculations.ts
│   │   └── validation.ts
│   ├── tests/
│   │   └── ITR1Application.test.js
│   ├── App.js
│   ├── App.css
│   ├── index.js
│   ├── index.css
│   └── reportWebVitals.js
├── package.json
├── .gitignore
└── README.md
```

## Usage

1. Fill out each section of the form step by step
2. Use the navigation buttons to move between sections
3. Save your progress using the "Save Draft" button
4. Load previously saved data using "Load Data"
5. Download the completed form as JSON using "Download JSON"

## Validation Rules

- PAN: Must follow the format **********
- Aadhaar: 12-digit number
- Email: Valid email format
- Mobile: 10-digit number starting with 1-9
- Pincode: 6-digit number
- IFSC: Valid IFSC code format

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Technical Features

### State Management
- **React Context API**: Global state management for form data
- **useReducer**: Complex state updates with predictable patterns
- **Local Storage**: Persistent data storage across sessions

### Validation System
- **Real-time Validation**: Instant feedback as users type
- **Custom Hooks**: Reusable validation logic
- **Error Handling**: Comprehensive error messages and warnings

### Tax Calculations
- **New Tax Regime**: Automatic calculation with updated slabs
- **Old Tax Regime**: Traditional tax calculation with deductions
- **Rebate Calculations**: Section 87A rebate computation
- **Deduction Limits**: Validation of deduction limits

### User Experience
- **Auto-save**: Prevents data loss with automatic saving
- **Progress Tracking**: Visual indication of completion status
- **Responsive Design**: Works on all device sizes
- **Accessibility**: Keyboard navigation and screen reader support

## Testing

The application includes comprehensive tests:

```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

### Test Coverage
- Component rendering tests
- Form validation tests
- Tax calculation tests
- User interaction tests
- Auto-save functionality tests

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- **Bundle Size**: Optimized for fast loading
- **Code Splitting**: Lazy loading of components
- **Caching**: Efficient caching strategies
- **Memory Management**: Proper cleanup of event listeners

## Security Considerations

- **Local Data Only**: No data transmitted to external servers
- **Input Sanitization**: Protection against XSS attacks
- **Secure Storage**: Encrypted local storage for sensitive data
- **Privacy**: No tracking or analytics

## Troubleshooting

### Common Issues

1. **Form not saving**: Check browser local storage permissions
2. **Validation errors**: Ensure all required fields are filled
3. **JSON download fails**: Check browser download permissions
4. **Performance issues**: Clear browser cache and restart

### Debug Mode

Enable debug mode by adding `?debug=true` to the URL for additional logging.

## Roadmap

### Upcoming Features
- [ ] PDF generation for printable returns
- [ ] Integration with e-filing portal
- [ ] Multi-language support
- [ ] Advanced tax planning tools
- [ ] Bulk data import from CSV/Excel

### Version History
- **v1.0.0**: Initial release with complete ITR-1 functionality
- **v1.1.0**: Added comprehensive testing and validation
- **v1.2.0**: Enhanced UI/UX and accessibility features

## API Reference

### ITR Context Methods

```javascript
const {
  formData,           // Current form data
  currentStep,        // Active step index
  updateFormData,     // Update form data
  setCurrentStep,     // Navigate to step
  validateForm,       // Validate entire form
  resetForm,          // Reset to initial state
  addNotification     // Show notification
} = useITR();
```

### Validation Hooks

```javascript
const {
  values,             // Form values
  errors,             // Validation errors
  warnings,           // Validation warnings
  isValid,            // Form validity status
  setValue,           // Update field value
  validateField       // Validate single field
} = useFormValidation(initialValues, rules);
```

## Disclaimer

This application is for educational and demonstration purposes. Please consult with a qualified tax professional for actual tax filing. The developers are not responsible for any errors in tax calculations or compliance issues.
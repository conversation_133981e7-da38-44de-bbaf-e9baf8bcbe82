import React from 'react';
import PropTypes from 'prop-types';

const Input = ({
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  disabled = false,
  required = false,
  error,
  warning,
  helpText,
  className = '',
  inputClassName = '',
  id,
  name,
  min,
  max,
  maxLength,
  pattern,
  autoComplete,
  ...props
}) => {
  const inputId = id || name || `input-${Math.random().toString(36).substr(2, 9)}`;
  
  const inputClasses = [
    'form-control',
    error && 'error',
    warning && 'warning',
    inputClassName
  ].filter(Boolean).join(' ');

  const containerClasses = [
    'form-group',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {label && (
        <label htmlFor={inputId} className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}
      
      <input
        id={inputId}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        className={inputClasses}
        min={min}
        max={max}
        maxLength={maxLength}
        pattern={pattern}
        autoComplete={autoComplete}
        {...props}
      />
      
      {helpText && !error && !warning && (
        <div className="help-text">{helpText}</div>
      )}
      
      {warning && (
        <div className="warning-text">{warning}</div>
      )}
      
      {error && (
        <div className="error-text">{error}</div>
      )}
    </div>
  );
};

Input.propTypes = {
  label: PropTypes.string,
  type: PropTypes.oneOf([
    'text', 'email', 'password', 'number', 'tel', 'url', 
    'search', 'date', 'time', 'datetime-local', 'month', 'week'
  ]),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  warning: PropTypes.string,
  helpText: PropTypes.string,
  className: PropTypes.string,
  inputClassName: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string,
  min: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  max: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  maxLength: PropTypes.number,
  pattern: PropTypes.string,
  autoComplete: PropTypes.string
};

export default Input;

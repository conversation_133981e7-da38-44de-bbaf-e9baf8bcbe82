import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

const Notification = ({
  id,
  message,
  type = 'info',
  duration = 5000,
  onClose,
  autoClose = true,
  closable = true
}) => {
  useEffect(() => {
    if (autoClose && duration > 0) {
      const timer = setTimeout(() => {
        onClose(id);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [id, duration, autoClose, onClose]);

  const handleClose = () => {
    if (onClose) {
      onClose(id);
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✕';
      case 'warning':
        return '⚠';
      case 'info':
      default:
        return 'ℹ';
    }
  };

  return (
    <div className={`notification ${type}`} onClick={handleClose}>
      <div className="notification-content">
        <span className="notification-icon">{getIcon()}</span>
        <span className="notification-message">{message}</span>
        {closable && (
          <button 
            className="notification-close"
            onClick={handleClose}
            aria-label="Close notification"
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
};

Notification.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  message: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['info', 'success', 'error', 'warning']),
  duration: PropTypes.number,
  onClose: PropTypes.func.isRequired,
  autoClose: PropTypes.bool,
  closable: PropTypes.bool
};

export default Notification;

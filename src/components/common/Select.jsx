import React from 'react';
import PropTypes from 'prop-types';

const Select = ({
  label,
  value,
  onChange,
  options = [],
  placeholder = 'Select an option',
  disabled = false,
  required = false,
  error,
  warning,
  helpText,
  className = '',
  selectClassName = '',
  id,
  name,
  ...props
}) => {
  const selectId = id || name || `select-${Math.random().toString(36).substr(2, 9)}`;
  
  const selectClasses = [
    'form-control',
    error && 'error',
    warning && 'warning',
    selectClassName
  ].filter(Boolean).join(' ');

  const containerClasses = [
    'form-group',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {label && (
        <label htmlFor={selectId} className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}
      
      <select
        id={selectId}
        name={name}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        className={selectClasses}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        
        {options.map((option, index) => {
          // Handle both string arrays and object arrays
          if (typeof option === 'string') {
            return (
              <option key={index} value={option}>
                {option}
              </option>
            );
          }
          
          // Handle object format: { value, label } or { code, name }
          const optionValue = option.value || option.code || option.id;
          const optionLabel = option.label || option.name || option.text || optionValue;
          
          return (
            <option key={optionValue} value={optionValue}>
              {optionLabel}
            </option>
          );
        })}
      </select>
      
      {helpText && !error && !warning && (
        <div className="help-text">{helpText}</div>
      )}
      
      {warning && (
        <div className="warning-text">{warning}</div>
      )}
      
      {error && (
        <div className="error-text">{error}</div>
      )}
    </div>
  );
};

Select.propTypes = {
  label: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        label: PropTypes.string,
        code: PropTypes.string,
        name: PropTypes.string,
        text: PropTypes.string,
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
      })
    ])
  ),
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  warning: PropTypes.string,
  helpText: PropTypes.string,
  className: PropTypes.string,
  selectClassName: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string
};

export default Select;

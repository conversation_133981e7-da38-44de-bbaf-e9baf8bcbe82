import React from 'react';
import PropTypes from 'prop-types';

const ProgressBar = ({
  value = 0,
  max = 100,
  showLabel = true,
  label,
  variant = 'primary',
  size = 'medium',
  animated = false,
  striped = false,
  className = ''
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  
  const progressClasses = [
    'progress-bar',
    `progress-bar-${variant}`,
    `progress-bar-${size}`,
    animated && 'progress-bar-animated',
    striped && 'progress-bar-striped',
    className
  ].filter(Boolean).join(' ');

  const displayLabel = label || (showLabel ? `${Math.round(percentage)}%` : '');

  return (
    <div className="progress-container">
      {displayLabel && (
        <div className="progress-label">
          {displayLabel}
        </div>
      )}
      <div className="progress">
        <div 
          className={progressClasses}
          style={{ width: `${percentage}%` }}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
          aria-label={label || `Progress: ${Math.round(percentage)}%`}
        >
          {showLabel && !label && (
            <span className="progress-text">{Math.round(percentage)}%</span>
          )}
        </div>
      </div>
    </div>
  );
};

ProgressBar.propTypes = {
  value: PropTypes.number,
  max: PropTypes.number,
  showLabel: PropTypes.bool,
  label: PropTypes.string,
  variant: PropTypes.oneOf([
    'primary', 'secondary', 'success', 'danger', 
    'warning', 'info', 'light', 'dark'
  ]),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  animated: PropTypes.bool,
  striped: PropTypes.bool,
  className: PropTypes.string
};

export default ProgressBar;

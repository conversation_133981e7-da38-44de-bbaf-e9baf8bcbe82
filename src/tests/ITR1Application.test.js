import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ITR1Application from '../components/ITR1Application';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

describe('ITR1Application', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
  });

  test('renders ITR-1 application header', () => {
    render(<ITR1Application />);
    expect(screen.getByText('ITR-1 (Sahaj)')).toBeInTheDocument();
    expect(screen.getByText('Assessment Year 2024-25 | Income Tax Return')).toBeInTheDocument();
  });

  test('renders all form steps', () => {
    render(<ITR1Application />);
    
    const expectedSteps = [
      'Personal Information',
      'Filing Status',
      'Income Details',
      'Deductions',
      'Tax Computation',
      'Tax Paid & Refund',
      'Schedules',
      'Verification'
    ];

    expectedSteps.forEach(step => {
      expect(screen.getByText(step)).toBeInTheDocument();
    });
  });

  test('starts with Personal Information step active', () => {
    render(<ITR1Application />);
    
    // Check if first step is active
    const firstStep = screen.getByText('Personal Information').closest('.step');
    expect(firstStep).toHaveClass('active');
  });

  test('renders personal information form fields', () => {
    render(<ITR1Application />);
    
    expect(screen.getByLabelText(/First Name/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Last Name/)).toBeInTheDocument();
    expect(screen.getByLabelText(/PAN/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Date of Birth/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Mobile Number/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email Address/)).toBeInTheDocument();
  });

  test('validates PAN format', async () => {
    render(<ITR1Application />);
    
    const panInput = screen.getByLabelText(/PAN/);
    fireEvent.change(panInput, { target: { value: 'INVALID' } });
    
    // Try to proceed to next step
    const nextButton = screen.getByText('Next →');
    fireEvent.click(nextButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Invalid PAN format/)).toBeInTheDocument();
    });
  });

  test('validates email format', async () => {
    render(<ITR1Application />);
    
    const emailInput = screen.getByLabelText(/Email Address/);
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    
    const nextButton = screen.getByText('Next →');
    fireEvent.click(nextButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Invalid email format/)).toBeInTheDocument();
    });
  });

  test('navigates to next step when valid data is entered', async () => {
    render(<ITR1Application />);
    
    // Fill required fields with valid data
    fireEvent.change(screen.getByLabelText(/Last Name/), { 
      target: { value: 'Doe' } 
    });
    fireEvent.change(screen.getByLabelText(/PAN/), { 
      target: { value: '**********' } 
    });
    fireEvent.change(screen.getByLabelText(/Date of Birth/), { 
      target: { value: '1990-01-01' } 
    });
    fireEvent.change(screen.getByLabelText(/Residence No/), { 
      target: { value: '123' } 
    });
    fireEvent.change(screen.getByLabelText(/Locality/), { 
      target: { value: 'Test Area' } 
    });
    fireEvent.change(screen.getByLabelText(/City/), { 
      target: { value: 'Test City' } 
    });
    fireEvent.change(screen.getByLabelText(/State/), { 
      target: { value: '09' } 
    });
    fireEvent.change(screen.getByLabelText(/PIN Code/), { 
      target: { value: '110001' } 
    });
    fireEvent.change(screen.getByLabelText(/Mobile Number/), { 
      target: { value: '9876543210' } 
    });
    fireEvent.change(screen.getByLabelText(/Email Address/), { 
      target: { value: '<EMAIL>' } 
    });
    
    const nextButton = screen.getByText('Next →');
    fireEvent.click(nextButton);
    
    await waitFor(() => {
      const filingStatusStep = screen.getByText('Filing Status').closest('.step');
      expect(filingStatusStep).toHaveClass('active');
    });
  });

  test('auto-saves form data', async () => {
    render(<ITR1Application />);
    
    const panInput = screen.getByLabelText(/PAN/);
    fireEvent.change(panInput, { target: { value: '**********' } });
    
    // Wait for auto-save
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'itr1_draft',
        expect.any(String)
      );
    }, { timeout: 35000 });
  });

  test('loads saved data on mount', () => {
    const savedData = JSON.stringify({
      personalInfo: {
        pan: '**********',
        assesseeName: { firstName: 'John', surNameOrOrgName: 'Doe' }
      }
    });
    
    localStorageMock.getItem.mockReturnValue(savedData);
    
    render(<ITR1Application />);
    
    expect(localStorageMock.getItem).toHaveBeenCalledWith('itr1_draft');
    expect(screen.getByDisplayValue('**********')).toBeInTheDocument();
  });

  test('calculates tax correctly for new regime', async () => {
    render(<ITR1Application />);
    
    // Navigate to income details step
    // ... (fill required fields and navigate)
    
    // Fill income details
    const grossSalaryInput = screen.getByLabelText(/Gross Salary/);
    fireEvent.change(grossSalaryInput, { target: { value: '600000' } });
    
    // Check if tax calculation is triggered
    await waitFor(() => {
      // Tax should be calculated based on new regime
      // For 6 lakhs: (600000 - 300000) * 0.05 = 15000
      // But this would need to navigate to tax computation step
    });
  });

  test('generates JSON file when form is complete', async () => {
    // Mock document.createElement and appendChild
    const mockAnchor = {
      href: '',
      download: '',
      click: jest.fn(),
    };
    jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor);
    jest.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    jest.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(<ITR1Application />);
    
    // Fill all required fields and navigate to last step
    // ... (this would be a complex test requiring full form completion)
    
    // Click download JSON button
    // const downloadButton = screen.getByText('📥 Download JSON');
    // fireEvent.click(downloadButton);
    
    // expect(mockAnchor.click).toHaveBeenCalled();
  });

  test('shows validation errors for incomplete form', async () => {
    render(<ITR1Application />);
    
    const nextButton = screen.getByText('Next →');
    fireEvent.click(nextButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Surname is mandatory/)).toBeInTheDocument();
      expect(screen.getByText(/PAN is mandatory/)).toBeInTheDocument();
    });
  });

  test('handles file upload for loading data', async () => {
    render(<ITR1Application />);
    
    const fileInput = screen.getByLabelText(/Load Data/);
    
    const file = new File(['{"ITR":{"ITR1":{}}}'], 'test.json', {
      type: 'application/json',
    });
    
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    // This would require mocking FileReader
    // await waitFor(() => {
    //   expect(screen.getByText(/Data loaded successfully/)).toBeInTheDocument();
    // });
  });

  test('displays notifications', async () => {
    render(<ITR1Application />);
    
    // Trigger an action that shows notification
    const saveButton = screen.getByText('💾 Save Draft');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Draft saved/)).toBeInTheDocument();
    });
  });
});

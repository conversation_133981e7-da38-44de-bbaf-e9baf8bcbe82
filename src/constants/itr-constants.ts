// Constants for ITR-1 application

import { StateCode, EmployerCategory } from '../types/itr-types';

// State codes for dropdown
export const stateCodes: StateCode[] = [
  { code: '01', name: 'Andaman and Nicobar Islands' },
  { code: '02', name: 'Andhra Pradesh' },
  { code: '03', name: 'Arunachal Pradesh' },
  { code: '04', name: 'Assam' },
  { code: '05', name: 'Bihar' },
  { code: '06', name: 'Chandigarh' },
  { code: '07', name: 'Dadra Nagar and Haveli' },
  { code: '08', name: 'Daman and Diu' },
  { code: '09', name: 'Delhi' },
  { code: '10', name: 'Goa' },
  { code: '11', name: 'Gujarat' },
  { code: '12', name: 'Haryana' },
  { code: '13', name: 'Himachal Pradesh' },
  { code: '14', name: 'Jammu and Kashmir' },
  { code: '15', name: 'Karnataka' },
  { code: '16', name: 'Kerala' },
  { code: '17', name: '<PERSON><PERSON><PERSON>weep' },
  { code: '18', name: 'Madhya Pradesh' },
  { code: '19', name: 'Maharashtra' },
  { code: '20', name: 'Manipur' },
  { code: '21', name: 'Meghalaya' },
  { code: '22', name: 'Mizoram' },
  { code: '23', name: 'Nagaland' },
  { code: '24', name: 'Odisha' },
  { code: '25', name: 'Puducherry' },
  { code: '26', name: 'Punjab' },
  { code: '27', name: 'Rajasthan' },
  { code: '28', name: 'Sikkim' },
  { code: '29', name: 'Tamil Nadu' },
  { code: '30', name: 'Tripura' },
  { code: '31', name: 'Uttar Pradesh' },
  { code: '32', name: 'West Bengal' },
  { code: '33', name: 'Chhattisgarh' },
  { code: '34', name: 'Uttarakhand' },
  { code: '35', name: 'Jharkhand' },
  { code: '36', name: 'Telangana' },
  { code: '37', name: 'Ladakh' }
];

// Employer categories
export const employerCategories: EmployerCategory[] = [
  { code: 'CGOV', name: 'Central Government' },
  { code: 'SGOV', name: 'State Government' },
  { code: 'PSU', name: 'Public Sector Unit' },
  { code: 'PE', name: 'Pensioners - Central Government' },
  { code: 'PESG', name: 'Pensioners - State Government' },
  { code: 'PEPS', name: 'Pensioners - Public sector undertaking' },
  { code: 'PEO', name: 'Pensioners - Others' },
  { code: 'OTH', name: 'Others' },
  { code: 'NA', name: 'Not Applicable' }
];

// Filing sections
export const filingSections = [
  { value: 11, label: '139(1) - On or before due date' },
  { value: 12, label: '139(4) - After due date' },
  { value: 17, label: '139(5) - Revised' },
  { value: 21, label: '139(8A) - Updated' }
];

// House property types
export const housePropertyTypes = [
  { value: 'S', label: 'Self Occupied' },
  { value: 'L', label: 'Let Out' },
  { value: 'D', label: 'Deemed Let Out' }
];

// Account types
export const accountTypes = [
  { value: 'SB', label: 'Savings Bank' },
  { value: 'CA', label: 'Current Account' },
  { value: 'CC', label: 'Cash Credit' },
  { value: 'OD', label: 'Over Draft' }
];

// Capacity types for verification
export const capacityTypes = [
  { value: 'S', label: 'Self' },
  { value: 'R', label: 'Representative Assessee' },
  { value: 'G', label: 'Guardian' }
];

// Tax regime information
export const taxRegimeInfo = {
  NEW: {
    name: 'New Tax Regime',
    description: 'Lower tax rates but most deductions not available',
    slabs: [
      { min: 0, max: 300000, rate: 0 },
      { min: 300000, max: 600000, rate: 5 },
      { min: 600000, max: 900000, rate: 10 },
      { min: 900000, max: 1200000, rate: 15 },
      { min: 1200000, max: 1500000, rate: 20 },
      { min: 1500000, max: Infinity, rate: 30 }
    ],
    rebate87A: {
      limit: 700000,
      maxRebate: 25000
    }
  },
  OLD: {
    name: 'Old Tax Regime',
    description: 'Higher tax rates but deductions under Chapter VI-A available',
    slabs: [
      { min: 0, max: 250000, rate: 0 },
      { min: 250000, max: 500000, rate: 5 },
      { min: 500000, max: 1000000, rate: 20 },
      { min: 1000000, max: Infinity, rate: 30 }
    ],
    rebate87A: {
      limit: 500000,
      maxRebate: 12500
    }
  }
};

// Deduction limits
export const deductionLimits = {
  section80C: 150000,
  section80CCC: 150000,
  section80CCDEmployeeOrSE: 150000,
  combined80C: 150000, // Combined limit for 80C + 80CCC + 80CCD(1)
  section80CCD1B: 50000,
  section80D: 100000,
  section80TTA: 10000,
  section80TTB: 50000,
  standardDeduction: 50000,
  professionalTax: 5000,
  familyPensionDeduction: 15000,
  housePropertyInterestSelfOccupied: 200000
};

// Form steps
export const formSteps = [
  'Personal Information',
  'Filing Status',
  'Income Details',
  'Deductions',
  'Tax Computation',
  'Tax Paid & Refund',
  'Schedules',
  'Verification'
];

// Validation messages
export const validationMessages = {
  required: 'This field is required',
  invalidPAN: 'Invalid PAN format. Should be like **********',
  invalidAadhaar: 'Invalid Aadhaar format. Should be 12 digits',
  invalidEmail: 'Invalid email format',
  invalidMobile: 'Invalid mobile number format',
  invalidPincode: 'Invalid PIN code format',
  invalidIFSC: 'Invalid IFSC code format',
  invalidBankAccount: 'Invalid bank account number format',
  exceedsLimit: 'Amount exceeds the allowed limit',
  futureDate: 'Future date is not allowed',
  minimumAge: 'Age should be at least 18 years',
  itr1NotApplicable: 'Total income exceeds Rs 50 lakhs. ITR-1 is not applicable',
  newRegimeDeductionNotAllowed: 'This deduction is not allowed in new tax regime'
};

// Help text for various fields
export const helpTexts = {
  pan: 'Permanent Account Number as per Income Tax Department',
  aadhaar: 'Aadhaar number is optional but recommended',
  grossSalary: 'Total salary before any deductions',
  standardDeduction: 'Standard deduction u/s 16(ia) - Maximum Rs. 50,000',
  professionalTax: 'Professional tax paid to state government - Maximum Rs. 5,000',
  housePropertyInterest: 'Interest paid on housing loan',
  section80C: 'PPF, ELSS, Life Insurance, etc. - Maximum Rs. 1,50,000',
  section80D: 'Medical insurance premiums - Maximum Rs. 1,00,000',
  section80TTA: 'Interest on savings account - Maximum Rs. 10,000',
  ifscCode: 'Indian Financial System Code of your bank branch',
  bankAccount: 'Bank account number for refund processing'
};

// Default form values
export const defaultFormValues = {
  creationInfo: {
    swVersionNo: '1.0',
    swCreatedBy: 'SW12345678',
    jsonCreatedBy: 'SW12345678',
    jsonCreationDate: new Date().toISOString().split('T')[0],
    intermediaryCity: 'Delhi',
    digest: ''
  },
  formITR1: {
    formName: 'ITR-1',
    description: 'ITR-1 for AY 2024-25',
    assessmentYear: '2024',
    schemaVer: 'Ver1.0',
    formVer: 'Ver1.0'
  },
  filingStatus: {
    returnFileSec: 11,
    optOutNewTaxRegime: 'N',
    seventhProvisio139: 'N',
    itrFilingDueDate: '2024-07-31'
  },
  personalInfo: {
    employerCategory: 'OTH',
    address: {
      countryCode: '91',
      countryCodeMobile: '91'
    }
  },
  incomeDeductions: {
    typeOfHP: 'S'
  },
  refund: {
    bankAccountDtls: {
      addtnlBankDetails: [{
        accountType: 'SB'
      }]
    }
  },
  verification: {
    capacity: 'S'
  }
};

// API endpoints (if needed for future enhancements)
export const apiEndpoints = {
  validatePAN: '/api/validate-pan',
  getBankDetails: '/api/bank-details',
  submitITR: '/api/submit-itr',
  saveAsDraft: '/api/save-draft',
  loadDraft: '/api/load-draft'
};

// File upload constraints
export const fileUploadConstraints = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['application/json'],
  allowedExtensions: ['.json']
};

// Auto-save configuration
export const autoSaveConfig = {
  interval: 30000, // 30 seconds
  storageKey: 'itr1_draft',
  maxRetries: 3
};

// Notification configuration
export const notificationConfig = {
  duration: 5000, // 5 seconds
  maxNotifications: 5,
  position: 'top-right'
};

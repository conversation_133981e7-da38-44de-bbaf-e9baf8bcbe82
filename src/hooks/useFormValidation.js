import { useState, useCallback, useMemo } from 'react';

/**
 * Custom hook for form validation
 * @param {object} initialValues - Initial form values
 * @param {object} validationRules - Validation rules for each field
 * @returns {object} - Form state and validation functions
 */
export const useFormValidation = (initialValues = {}, validationRules = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [touched, setTouched] = useState({});

  // Validate a single field
  const validateField = useCallback((fieldName, value) => {
    const rules = validationRules[fieldName];
    if (!rules) return { errors: [], warnings: [] };

    const fieldErrors = [];
    const fieldWarnings = [];

    // Required validation
    if (rules.required && (!value || value.toString().trim() === '')) {
      fieldErrors.push(rules.requiredMessage || 'This field is required');
    }

    // Only run other validations if field has a value
    if (value && value.toString().trim() !== '') {
      // Pattern validation
      if (rules.pattern && !rules.pattern.test(value)) {
        fieldErrors.push(rules.patternMessage || 'Invalid format');
      }

      // Min length validation
      if (rules.minLength && value.toString().length < rules.minLength) {
        fieldErrors.push(rules.minLengthMessage || `Minimum ${rules.minLength} characters required`);
      }

      // Max length validation
      if (rules.maxLength && value.toString().length > rules.maxLength) {
        fieldErrors.push(rules.maxLengthMessage || `Maximum ${rules.maxLength} characters allowed`);
      }

      // Min value validation
      if (rules.min !== undefined && Number(value) < rules.min) {
        fieldErrors.push(rules.minMessage || `Minimum value is ${rules.min}`);
      }

      // Max value validation
      if (rules.max !== undefined && Number(value) > rules.max) {
        fieldErrors.push(rules.maxMessage || `Maximum value is ${rules.max}`);
      }

      // Custom validation function
      if (rules.validate && typeof rules.validate === 'function') {
        const customResult = rules.validate(value, values);
        if (customResult && customResult !== true) {
          if (typeof customResult === 'string') {
            fieldErrors.push(customResult);
          } else if (customResult.error) {
            fieldErrors.push(customResult.error);
          }
          if (customResult.warning) {
            fieldWarnings.push(customResult.warning);
          }
        }
      }

      // Warning validations
      if (rules.warning && typeof rules.warning === 'function') {
        const warningResult = rules.warning(value, values);
        if (warningResult && typeof warningResult === 'string') {
          fieldWarnings.push(warningResult);
        }
      }
    }

    return { errors: fieldErrors, warnings: fieldWarnings };
  }, [validationRules, values]);

  // Validate all fields
  const validateForm = useCallback(() => {
    const newErrors = {};
    const newWarnings = {};

    Object.keys(validationRules).forEach(fieldName => {
      const fieldValue = values[fieldName];
      const { errors: fieldErrors, warnings: fieldWarnings } = validateField(fieldName, fieldValue);
      
      if (fieldErrors.length > 0) {
        newErrors[fieldName] = fieldErrors;
      }
      if (fieldWarnings.length > 0) {
        newWarnings[fieldName] = fieldWarnings;
      }
    });

    setErrors(newErrors);
    setWarnings(newWarnings);

    return {
      isValid: Object.keys(newErrors).length === 0,
      errors: newErrors,
      warnings: newWarnings
    };
  }, [values, validationRules, validateField]);

  // Update field value
  const setValue = useCallback((fieldName, value) => {
    setValues(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Validate field if it has been touched
    if (touched[fieldName]) {
      const { errors: fieldErrors, warnings: fieldWarnings } = validateField(fieldName, value);
      
      setErrors(prev => ({
        ...prev,
        [fieldName]: fieldErrors.length > 0 ? fieldErrors : undefined
      }));
      
      setWarnings(prev => ({
        ...prev,
        [fieldName]: fieldWarnings.length > 0 ? fieldWarnings : undefined
      }));
    }
  }, [touched, validateField]);

  // Mark field as touched
  const setFieldTouched = useCallback((fieldName, isTouched = true) => {
    setTouched(prev => ({
      ...prev,
      [fieldName]: isTouched
    }));

    // Validate field when it becomes touched
    if (isTouched) {
      const fieldValue = values[fieldName];
      const { errors: fieldErrors, warnings: fieldWarnings } = validateField(fieldName, fieldValue);
      
      setErrors(prev => ({
        ...prev,
        [fieldName]: fieldErrors.length > 0 ? fieldErrors : undefined
      }));
      
      setWarnings(prev => ({
        ...prev,
        [fieldName]: fieldWarnings.length > 0 ? fieldWarnings : undefined
      }));
    }
  }, [values, validateField]);

  // Reset form
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setWarnings({});
    setTouched({});
  }, [initialValues]);

  // Check if form is valid
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);

  // Check if form has been modified
  const isDirty = useMemo(() => {
    return JSON.stringify(values) !== JSON.stringify(initialValues);
  }, [values, initialValues]);

  return {
    values,
    errors,
    warnings,
    touched,
    isValid,
    isDirty,
    setValue,
    setFieldTouched,
    validateField,
    validateForm,
    resetForm
  };
};

export default useFormValidation;

import { useState, useCallback } from 'react';

/**
 * Custom hook for managing notifications
 * @param {number} maxNotifications - Maximum number of notifications to show
 * @returns {object} - Notifications array and management functions
 */
export const useNotifications = (maxNotifications = 5) => {
  const [notifications, setNotifications] = useState([]);

  // Add a new notification
  const addNotification = useCallback((message, type = 'info', options = {}) => {
    const id = Date.now() + Math.random();
    const notification = {
      id,
      message,
      type,
      timestamp: new Date(),
      ...options
    };

    setNotifications(prev => {
      const newNotifications = [notification, ...prev];
      // Keep only the latest notifications up to the max limit
      return newNotifications.slice(0, maxNotifications);
    });

    return id;
  }, [maxNotifications]);

  // Remove a notification by ID
  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  // Clear all notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Add specific notification types
  const addSuccess = useCallback((message, options) => {
    return addNotification(message, 'success', options);
  }, [addNotification]);

  const addError = useCallback((message, options) => {
    return addNotification(message, 'error', options);
  }, [addNotification]);

  const addWarning = useCallback((message, options) => {
    return addNotification(message, 'warning', options);
  }, [addNotification]);

  const addInfo = useCallback((message, options) => {
    return addNotification(message, 'info', options);
  }, [addNotification]);

  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    addSuccess,
    addError,
    addWarning,
    addInfo
  };
};

export default useNotifications;

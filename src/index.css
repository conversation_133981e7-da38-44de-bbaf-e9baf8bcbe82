body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

#root {
  min-height: 100vh;
}

/* ITR-1 Application Styles */
.itr-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 2px solid #e0e0e0;
}

.header h1 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 600;
}

.header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1.1rem;
}

/* Stepper Styles */
.stepper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
  padding: 0 20px;
  overflow-x: auto;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background-color: #3498db;
  color: white;
}

.step.completed .step-number {
  background-color: #27ae60;
  color: white;
}

.step:not(.active):not(.completed) .step-number {
  background-color: #ecf0f1;
  color: #95a5a6;
}

.step-title {
  font-size: 0.9rem;
  text-align: center;
  color: #7f8c8d;
  font-weight: 500;
}

.step.active .step-title {
  color: #3498db;
  font-weight: 600;
}

.step.completed .step-title {
  color: #27ae60;
  font-weight: 600;
}

/* Form Styles */
.form-section {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-section h2 {
  color: #2c3e50;
  margin: 0 0 25px 0;
  font-size: 1.8rem;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.form-section h3 {
  color: #34495e;
  margin: 25px 0 15px 0;
  font-size: 1.3rem;
  font-weight: 500;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.deductions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group.calculated {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  border-left: 4px solid #17a2b8;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background-color: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-group input[readonly] {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #e74c3c;
  background-color: #fdf2f2;
}

.help-text {
  font-size: 0.85rem;
  color: #7f8c8d;
  margin-top: 5px;
  font-style: italic;
}

.error-text {
  font-size: 0.85rem;
  color: #e74c3c;
  margin-top: 5px;
  font-weight: 500;
}

.warning-text {
  font-size: 0.85rem;
  color: #f39c12;
  margin-top: 5px;
  font-weight: 500;
}

/* Radio Group Styles */
.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.radio-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
  margin-bottom: 0;
}

.radio-group input[type="radio"] {
  margin-right: 8px;
  width: auto;
}

/* Info and Message Boxes */
.info-box {
  background-color: #e8f4fd;
  border: 1px solid #bee5eb;
  border-radius: 5px;
  padding: 15px;
  margin-top: 15px;
}

.info-box p {
  margin: 0;
  color: #0c5460;
  font-size: 0.95rem;
}

.warning-box {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
}

.warning-box strong {
  color: #856404;
}

.error-box {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
  color: #721c24;
}

.success-box {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
  color: #155724;
}

/* Income Section Styles */
.income-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.income-section h3 {
  margin-top: 0;
  color: #2c3e50;
  border-bottom: 1px solid #bdc3c7;
  padding-bottom: 8px;
}

/* Tax Computation Styles */
.tax-summary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 10px;
  margin: 20px 0;
}

.tax-summary h3 {
  margin-top: 0;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.tax-summary .form-group input {
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  color: #333;
  font-weight: 600;
}

.tax-summary .help-text {
  color: rgba(255, 255, 255, 0.8);
}

/* Navigation Buttons */
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding: 20px 0;
  border-top: 1px solid #e0e0e0;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(149, 165, 166, 0.3);
}

.btn-success {
  background-color: #27ae60;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #229954;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

/* Notifications */
.notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

.notification {
  padding: 15px 20px;
  margin-bottom: 10px;
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease-out;
  position: relative;
  cursor: pointer;
}

.notification.info {
  background-color: #d1ecf1;
  border-left: 4px solid #17a2b8;
  color: #0c5460;
}

.notification.success {
  background-color: #d4edda;
  border-left: 4px solid #28a745;
  color: #155724;
}

.notification.error {
  background-color: #f8d7da;
  border-left: 4px solid #dc3545;
  color: #721c24;
}

.notification.warning {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
  color: #856404;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* File Upload Styles */
.file-upload {
  margin: 20px 0;
  padding: 20px;
  border: 2px dashed #bdc3c7;
  border-radius: 8px;
  text-align: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.file-upload:hover {
  border-color: #3498db;
  background-color: #f0f8ff;
}

.file-upload input[type="file"] {
  margin: 10px 0;
}

/* Auto-save indicator */
.auto-save-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #2c3e50;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.auto-save-indicator.visible {
  opacity: 1;
}

/* Progress indicator */
.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #ecf0f1;
  border-radius: 3px;
  overflow: hidden;
  margin: 20px 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
  border-radius: 3px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .itr-container {
    padding: 10px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .stepper {
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
  }

  .step {
    min-width: 100px;
  }

  .form-grid,
  .deductions-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .form-section {
    padding: 20px 15px;
  }

  .navigation-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .notifications {
    left: 10px;
    right: 10px;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.5rem;
  }

  .step-title {
    font-size: 0.8rem;
  }

  .form-section h2 {
    font-size: 1.5rem;
  }

  .form-section h3 {
    font-size: 1.2rem;
  }
}

/* Print Styles */
@media print {
  .stepper,
  .navigation-buttons,
  .notifications,
  .auto-save-indicator,
  .file-upload {
    display: none !important;
  }

  .itr-container {
    max-width: none;
    padding: 0;
    box-shadow: none;
  }

  .form-section {
    box-shadow: none;
    border: 1px solid #ccc;
    page-break-inside: avoid;
  }
}
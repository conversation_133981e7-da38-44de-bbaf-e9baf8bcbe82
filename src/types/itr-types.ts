// ITR-1 Form Types and Interfaces

export interface AssesseeName {
  firstName: string;
  middleName: string;
  surNameOrOrgName: string;
}

export interface Address {
  residenceNo: string;
  residenceName: string;
  roadOrStreet: string;
  localityOrArea: string;
  cityOrTownOrDistrict: string;
  stateCode: string;
  countryCode: string;
  pinCode: string;
  countryCodeMobile: string;
  mobileNo: string;
  emailAddress: string;
}

export interface PersonalInfo {
  assesseeName: AssesseeName;
  pan: string;
  address: Address;
  dob: string;
  employerCategory: string;
  aadhaarCardNo: string;
}

export interface CreationInfo {
  swVersionNo: string;
  swCreatedBy: string;
  jsonCreatedBy: string;
  jsonCreationDate: string;
  intermediaryCity: string;
  digest: string;
}

export interface FormITR1 {
  formName: string;
  description: string;
  assessmentYear: string;
  schemaVer: string;
  formVer: string;
}

export interface FilingStatus {
  returnFileSec: number;
  optOutNewTaxRegime: string;
  seventhProvisio139: string;
  itrFilingDueDate: string;
}

export interface IncomeDeductions {
  grossSalary: number;
  salary: number;
  perquisitesValue: number;
  profitsInSalary: number;
  incomeNotified89A: number;
  netSalary: number;
  deductionUs16: number;
  deductionUs16ia: number;
  entertainmentAlw16ii: number;
  professionalTaxUs16iii: number;
  incomeFromSal: number;
  typeOfHP: string;
  grossRentReceived: number;
  taxPaidlocalAuth: number;
  annualValue: number;
  standardDeduction: number;
  interestPayable: number;
  totalIncomeOfHP: number;
  incomeOthSrc: number;
  deductionUs57iia: number;
  grossTotIncome: number;
  totalIncome: number;
}

export interface UsrDeductUndChapVIA {
  section80C: number;
  section80CCC: number;
  section80CCDEmployeeOrSE: number;
  section80CCD1B: number;
  section80CCDEmployer: number;
  section80D: number;
  section80DD: number;
  section80DDB: number;
  section80E: number;
  section80EE: number;
  section80G: number;
  section80GG: number;
  section80GGA: number;
  section80GGC: number;
  section80U: number;
  section80TTA: number;
  section80TTB: number;
  totalChapVIADeductions: number;
}

export interface TaxComputation {
  totalTaxPayable: number;
  rebate87A: number;
  taxPayableOnRebate: number;
  educationCess: number;
  grossTaxLiability: number;
  section89: number;
  netTaxLiability: number;
  totalIntrstPay: number;
  totTaxPlusIntrstPay: number;
}

export interface TaxesPaid {
  advanceTax: number;
  tds: number;
  tcs: number;
  selfAssessmentTax: number;
  totalTaxesPaid: number;
}

export interface TaxPaid {
  taxesPaid: TaxesPaid;
  balTaxPayable: number;
}

export interface BankDetails {
  ifscCode: string;
  bankName: string;
  bankAccountNo: string;
  accountType: string;
}

export interface BankAccountDtls {
  addtnlBankDetails: BankDetails[];
}

export interface Refund {
  refundDue: number;
  bankAccountDtls: BankAccountDtls;
}

export interface Schedule80G {
  totalDonationsUs80GCash: number;
  totalDonationsUs80GOtherMode: number;
  totalDonationsUs80G: number;
  totalEligibleDonationsUs80G: number;
  donations: any[];
}

export interface Sec80DSelfFamSrCtznHealth {
  seniorCitizenFlag: string;
  selfAndFamily: number;
  healthInsPremSlfFam: number;
  prevHlthChckUpSlfFam: number;
  parentsSeniorCitizenFlag: string;
  parents: number;
  healthInsPremParents: number;
  prevHlthChckUpParents: number;
  eligibleAmountOfDedn: number;
}

export interface Schedule80D {
  sec80DSelfFamSrCtznHealth: Sec80DSelfFamSrCtznHealth;
}

export interface TdsOnSalaries {
  tdsOnSalary: any[];
  totalTDSonSalaries: number;
}

export interface TdsOnOthThanSals {
  tdsOnOthThanSal: any[];
  totalTDSonOthThanSals: number;
}

export interface Schedules {
  schedule80G: Schedule80G;
  schedule80D: Schedule80D;
  tdsOnSalaries: TdsOnSalaries;
  tdsOnOthThanSals: TdsOnOthThanSals;
}

export interface Declaration {
  assesseeVerName: string;
  fatherName: string;
  assesseeVerPAN: string;
}

export interface Verification {
  declaration: Declaration;
  capacity: string;
  place: string;
}

export interface ITR1FormData {
  creationInfo: CreationInfo;
  formITR1: FormITR1;
  personalInfo: PersonalInfo;
  filingStatus: FilingStatus;
  incomeDeductions: IncomeDeductions;
  usrDeductUndChapVIA: UsrDeductUndChapVIA;
  taxComputation: TaxComputation;
  taxPaid: TaxPaid;
  refund: Refund;
  schedules: Schedules;
  verification: Verification;
}

export interface ValidationError {
  [key: string]: string[];
}

export interface ValidationWarning {
  [key: string]: string[];
}

export interface Notification {
  id: number;
  message: string;
  type: 'info' | 'success' | 'error' | 'warning';
}

export interface TaxCalculations {
  totalTaxPayable?: number;
  rebate87A?: number;
  taxPayableOnRebate?: number;
  educationCess?: number;
  grossTaxLiability?: number;
  netTaxLiability?: number;
}

export interface StateCode {
  code: string;
  name: string;
}

export interface EmployerCategory {
  code: string;
  name: string;
}

// Validation Rules
export interface ValidationRules {
  pan: RegExp;
  aadhaar: RegExp;
  email: RegExp;
  mobile: RegExp;
  pincode: RegExp;
  ifsc: RegExp;
}

// Tax Regime Types
export type TaxRegime = 'NEW' | 'OLD';

// Filing Section Types
export type FilingSection = 11 | 12 | 17 | 21;

// House Property Types
export type HousePropertyType = 'S' | 'L' | 'D';

// Account Types
export type AccountType = 'SB' | 'CA' | 'CC' | 'OD';

// Capacity Types
export type CapacityType = 'S' | 'R' | 'G';

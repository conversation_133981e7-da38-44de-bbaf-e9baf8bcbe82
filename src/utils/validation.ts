// Validation utilities for ITR-1 form

import { ITR1FormData, ValidationError, ValidationWarning } from '../types/itr-types';

// Validation regex patterns
export const validationRules = {
  pan: /^[A-Z]{3}[P][A-Z][0-9]{4}[A-Z]$/,
  aadhaar: /^[0-9]{12}$/,
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  mobile: /^[1-9][0-9]{9}$/,
  pincode: /^[1-9][0-9]{5}$/,
  ifsc: /^[A-Z]{4}[0][A-Z0-9]{6}$/,
  bankAccount: /^[0-9]{9,18}$/
};

/**
 * Validate PAN format
 */
export const validatePAN = (pan: string): boolean => {
  return validationRules.pan.test(pan);
};

/**
 * Validate Aadhaar format
 */
export const validateAadhaar = (aadhaar: string): boolean => {
  return validationRules.aadhaar.test(aadhaar);
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  return validationRules.email.test(email);
};

/**
 * Validate mobile number format
 */
export const validateMobile = (mobile: string): boolean => {
  return validationRules.mobile.test(mobile);
};

/**
 * Validate PIN code format
 */
export const validatePincode = (pincode: string): boolean => {
  return validationRules.pincode.test(pincode);
};

/**
 * Validate IFSC code format
 */
export const validateIFSC = (ifsc: string): boolean => {
  return validationRules.ifsc.test(ifsc);
};

/**
 * Validate bank account number format
 */
export const validateBankAccount = (accountNo: string): boolean => {
  return validationRules.bankAccount.test(accountNo);
};

/**
 * Validate date format and check if it's not in future
 */
export const validateDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  const today = new Date();
  return date <= today && !isNaN(date.getTime());
};

/**
 * Validate age based on date of birth (should be at least 18 years)
 */
export const validateAge = (dob: string): boolean => {
  const birthDate = new Date(dob);
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 >= 18;
  }
  return age >= 18;
};

/**
 * Validate ITR-1 eligibility based on total income
 */
export const validateITR1Eligibility = (totalIncome: number): boolean => {
  return totalIncome <= 5000000; // Rs. 50 lakhs limit for ITR-1
};

/**
 * Validate house property interest limits
 */
export const validateHousePropertyInterest = (
  typeOfHP: string,
  interestAmount: number
): boolean => {
  if (typeOfHP === 'S') {
    return interestAmount <= 200000; // Rs. 2 lakhs limit for self-occupied
  }
  return true; // No limit for let-out property
};

/**
 * Validate deduction limits for old regime
 */
export const validateDeductionLimits = (
  deductions: any,
  isNewRegime: boolean
): ValidationError => {
  const errors: ValidationError = {};

  if (isNewRegime) {
    // New regime - most deductions not allowed
    const notAllowedDeductions = [
      'section80C', 'section80CCC', 'section80CCDEmployeeOrSE', 'section80D',
      'section80DD', 'section80DDB', 'section80E', 'section80EE', 'section80G',
      'section80GG', 'section80GGA', 'section80GGC', 'section80U', 'section80TTA', 'section80TTB'
    ];

    notAllowedDeductions.forEach(deduction => {
      if (deductions[deduction] > 0) {
        errors[deduction] = ['This deduction is not allowed in new tax regime'];
      }
    });
  } else {
    // Old regime - validate limits
    const section80CCombined = (deductions.section80C || 0) +
                               (deductions.section80CCC || 0) +
                               (deductions.section80CCDEmployeeOrSE || 0);

    if (section80CCombined > 150000) {
      errors['combined80C'] = ['Combined deduction u/s 80C, 80CCC & 80CCD(1) cannot exceed Rs 1,50,000'];
    }

    if (deductions.section80CCD1B > 50000) {
      errors['section80CCD1B'] = ['Deduction u/s 80CCD(1B) cannot exceed Rs 50,000'];
    }

    if (deductions.section80D > 100000) {
      errors['section80D'] = ['Deduction u/s 80D cannot exceed Rs 1,00,000'];
    }

    if (deductions.section80TTA > 10000) {
      errors['section80TTA'] = ['Deduction u/s 80TTA cannot exceed Rs 10,000'];
    }
  }

  return errors;
};

/**
 * Comprehensive form validation
 */
export const validateForm = (formData: ITR1FormData): {
  errors: ValidationError;
  warnings: ValidationWarning;
} => {
  const errors: ValidationError = {};
  const warnings: ValidationWarning = {};

  // Personal Information validations
  if (!formData.personalInfo.assesseeName.surNameOrOrgName) {
    errors['personalInfo.assesseeName.surNameOrOrgName'] = ['Surname is mandatory'];
  }

  if (!formData.personalInfo.pan) {
    errors['personalInfo.pan'] = ['PAN is mandatory'];
  } else if (!validatePAN(formData.personalInfo.pan)) {
    errors['personalInfo.pan'] = ['Invalid PAN format'];
  }

  if (!formData.personalInfo.dob) {
    errors['personalInfo.dob'] = ['Date of birth is mandatory'];
  } else if (!validateDate(formData.personalInfo.dob)) {
    errors['personalInfo.dob'] = ['Invalid date or future date not allowed'];
  } else if (!validateAge(formData.personalInfo.dob)) {
    warnings['personalInfo.dob'] = ['Age should be at least 18 years'];
  }

  // Address validations
  const address = formData.personalInfo.address;
  
  if (!address.residenceNo) {
    errors['personalInfo.address.residenceNo'] = ['Residence number is mandatory'];
  }

  if (!address.localityOrArea) {
    errors['personalInfo.address.localityOrArea'] = ['Locality/Area is mandatory'];
  }

  if (!address.cityOrTownOrDistrict) {
    errors['personalInfo.address.cityOrTownOrDistrict'] = ['City/Town/District is mandatory'];
  }

  if (!address.stateCode) {
    errors['personalInfo.address.stateCode'] = ['State is mandatory'];
  }

  if (!address.pinCode) {
    errors['personalInfo.address.pinCode'] = ['PIN Code is mandatory'];
  } else if (!validatePincode(address.pinCode)) {
    errors['personalInfo.address.pinCode'] = ['Invalid PIN Code format'];
  }

  if (!address.mobileNo) {
    errors['personalInfo.address.mobileNo'] = ['Mobile number is mandatory'];
  } else if (!validateMobile(address.mobileNo)) {
    errors['personalInfo.address.mobileNo'] = ['Invalid mobile number format'];
  }

  if (!address.emailAddress) {
    errors['personalInfo.address.emailAddress'] = ['Email address is mandatory'];
  } else if (!validateEmail(address.emailAddress)) {
    errors['personalInfo.address.emailAddress'] = ['Invalid email format'];
  }

  // Aadhaar validation (optional but if provided should be valid)
  if (formData.personalInfo.aadhaarCardNo && !validateAadhaar(formData.personalInfo.aadhaarCardNo)) {
    errors['personalInfo.aadhaarCardNo'] = ['Invalid Aadhaar format'];
  }

  // Income validations
  if (!validateITR1Eligibility(formData.incomeDeductions.totalIncome)) {
    errors['incomeDeductions.totalIncome'] = ['Total income exceeds Rs 50 lakhs limit for ITR-1'];
  }

  // House property validations
  if (!validateHousePropertyInterest(
    formData.incomeDeductions.typeOfHP,
    formData.incomeDeductions.interestPayable
  )) {
    errors['incomeDeductions.interestPayable'] = [
      'Interest on housing loan for self-occupied property cannot exceed Rs 2,00,000'
    ];
  }

  // Standard deduction validation
  if (formData.incomeDeductions.deductionUs16ia > 50000) {
    errors['incomeDeductions.deductionUs16ia'] = ['Standard deduction cannot exceed Rs 50,000'];
  }

  // Professional tax validation
  if (formData.incomeDeductions.professionalTaxUs16iii > 5000) {
    errors['incomeDeductions.professionalTaxUs16iii'] = ['Professional tax cannot exceed Rs 5,000'];
  }

  // Deduction validations
  const isNewRegime = formData.filingStatus.optOutNewTaxRegime === 'N';
  const deductionErrors = validateDeductionLimits(formData.usrDeductUndChapVIA, isNewRegime);
  Object.assign(errors, deductionErrors);

  // Bank details validation (if refund is due)
  if (formData.refund.refundDue > 0) {
    const bankDetails = formData.refund.bankAccountDtls.addtnlBankDetails[0];
    
    if (!bankDetails.ifscCode) {
      errors['refund.bankAccountDtls.ifscCode'] = ['IFSC code is mandatory for refund'];
    } else if (!validateIFSC(bankDetails.ifscCode)) {
      errors['refund.bankAccountDtls.ifscCode'] = ['Invalid IFSC code format'];
    }

    if (!bankDetails.bankAccountNo) {
      errors['refund.bankAccountDtls.bankAccountNo'] = ['Bank account number is mandatory for refund'];
    } else if (!validateBankAccount(bankDetails.bankAccountNo)) {
      errors['refund.bankAccountDtls.bankAccountNo'] = ['Invalid bank account number format'];
    }

    if (!bankDetails.bankName) {
      errors['refund.bankAccountDtls.bankName'] = ['Bank name is mandatory for refund'];
    }
  }

  // Verification validations
  if (!formData.verification.declaration.assesseeVerName) {
    errors['verification.declaration.assesseeVerName'] = ['Assessee name for verification is mandatory'];
  }

  if (!formData.verification.declaration.fatherName) {
    errors['verification.declaration.fatherName'] = ['Father name is mandatory'];
  }

  if (!formData.verification.declaration.assesseeVerPAN) {
    errors['verification.declaration.assesseeVerPAN'] = ['PAN for verification is mandatory'];
  } else if (!validatePAN(formData.verification.declaration.assesseeVerPAN)) {
    errors['verification.declaration.assesseeVerPAN'] = ['Invalid PAN format in verification'];
  }

  if (!formData.verification.place) {
    errors['verification.place'] = ['Place is mandatory'];
  }

  // Cross-field validations (warnings)
  if (formData.personalInfo.pan && formData.verification.declaration.assesseeVerPAN &&
      formData.personalInfo.pan !== formData.verification.declaration.assesseeVerPAN) {
    warnings['verification.declaration.assesseeVerPAN'] = ['PAN does not match with personal info PAN'];
  }

  const fullName = `${formData.personalInfo.assesseeName.firstName} ${formData.personalInfo.assesseeName.middleName} ${formData.personalInfo.assesseeName.surNameOrOrgName}`.trim();
  if (fullName && formData.verification.declaration.assesseeVerName &&
      fullName.toLowerCase() !== formData.verification.declaration.assesseeVerName.toLowerCase()) {
    warnings['verification.declaration.assesseeVerName'] = ['Name does not match with personal info name'];
  }

  return { errors, warnings };
};

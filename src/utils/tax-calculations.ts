// Tax Calculation Utilities for ITR-1

import { TaxCalculations, ITR1FormData } from '../types/itr-types';

/**
 * Calculate tax under new tax regime (AY 2024-25)
 */
export const calculateNewRegimeTax = (totalIncome: number): TaxCalculations => {
  let tax = 0;
  let rebate87A = 0;

  // New tax regime slabs for AY 2024-25
  if (totalIncome <= 300000) {
    tax = 0;
  } else if (totalIncome <= 600000) {
    tax = (totalIncome - 300000) * 0.05;
  } else if (totalIncome <= 900000) {
    tax = 15000 + (totalIncome - 600000) * 0.10;
  } else if (totalIncome <= 1200000) {
    tax = 45000 + (totalIncome - 900000) * 0.15;
  } else if (totalIncome <= 1500000) {
    tax = 90000 + (totalIncome - 1200000) * 0.20;
  } else {
    tax = 150000 + (totalIncome - 1500000) * 0.30;
  }

  // Rebate 87A for new regime
  if (totalIncome <= 700000) {
    rebate87A = Math.min(tax, 25000);
  }

  const taxAfterRebate = Math.max(0, tax - rebate87A);
  const educationCess = taxAfterRebate * 0.04;
  const totalTaxLiability = taxAfterRebate + educationCess;

  return {
    totalTaxPayable: Math.round(tax),
    rebate87A: Math.round(rebate87A),
    taxPayableOnRebate: Math.round(taxAfterRebate),
    educationCess: Math.round(educationCess),
    grossTaxLiability: Math.round(totalTaxLiability),
    netTaxLiability: Math.round(totalTaxLiability)
  };
};

/**
 * Calculate tax under old tax regime (AY 2024-25)
 */
export const calculateOldRegimeTax = (totalIncome: number): TaxCalculations => {
  let tax = 0;
  let rebate87A = 0;

  // Old tax regime slabs
  if (totalIncome <= 250000) {
    tax = 0;
  } else if (totalIncome <= 500000) {
    tax = (totalIncome - 250000) * 0.05;
  } else if (totalIncome <= 1000000) {
    tax = 12500 + (totalIncome - 500000) * 0.20;
  } else {
    tax = 112500 + (totalIncome - 1000000) * 0.30;
  }

  // Rebate 87A for old regime
  if (totalIncome <= 500000) {
    rebate87A = Math.min(tax, 12500);
  }

  const taxAfterRebate = Math.max(0, tax - rebate87A);
  const educationCess = taxAfterRebate * 0.04;
  const totalTaxLiability = taxAfterRebate + educationCess;

  return {
    totalTaxPayable: Math.round(tax),
    rebate87A: Math.round(rebate87A),
    taxPayableOnRebate: Math.round(taxAfterRebate),
    educationCess: Math.round(educationCess),
    grossTaxLiability: Math.round(totalTaxLiability),
    netTaxLiability: Math.round(totalTaxLiability)
  };
};

/**
 * Calculate income from salary
 */
export const calculateSalaryIncome = (
  grossSalary: number,
  standardDeduction: number = 0,
  professionalTax: number = 0
): number => {
  return Math.max(0, grossSalary - standardDeduction - professionalTax);
};

/**
 * Calculate house property income
 */
export const calculateHousePropertyIncome = (
  typeOfHP: string,
  grossRentReceived: number = 0,
  taxPaidToLocalAuth: number = 0,
  interestOnLoan: number = 0
): { annualValue: number; standardDeduction: number; totalIncome: number } => {
  if (typeOfHP === 'S') {
    // Self-occupied property
    const loss = Math.min(200000, interestOnLoan);
    return {
      annualValue: 0,
      standardDeduction: 0,
      totalIncome: -loss
    };
  } else {
    // Let-out or deemed let-out property
    const annualValue = Math.max(0, grossRentReceived - taxPaidToLocalAuth);
    const standardDeduction = annualValue * 0.30;
    const totalIncome = annualValue - standardDeduction - interestOnLoan;
    
    return {
      annualValue,
      standardDeduction,
      totalIncome
    };
  }
};

/**
 * Calculate gross total income
 */
export const calculateGrossTotalIncome = (
  salaryIncome: number,
  housePropertyIncome: number,
  otherSourcesIncome: number
): number => {
  // House property loss is limited to Rs. 2,00,000
  const adjustedHPIncome = Math.max(-200000, housePropertyIncome);
  return salaryIncome + adjustedHPIncome + otherSourcesIncome;
};

/**
 * Calculate total deductions under Chapter VI-A
 */
export const calculateChapterVIADeductions = (deductions: any): number => {
  const deductionKeys = [
    'section80C', 'section80CCC', 'section80CCDEmployeeOrSE', 'section80CCD1B',
    'section80CCDEmployer', 'section80D', 'section80DD', 'section80DDB',
    'section80E', 'section80EE', 'section80G', 'section80GG', 'section80GGA',
    'section80GGC', 'section80U', 'section80TTA', 'section80TTB'
  ];

  return deductionKeys.reduce((total, key) => {
    return total + (deductions[key] || 0);
  }, 0);
};

/**
 * Calculate total income after deductions
 */
export const calculateTotalIncome = (
  grossTotalIncome: number,
  totalDeductions: number
): number => {
  return Math.max(0, grossTotalIncome - totalDeductions);
};

/**
 * Validate deduction limits for old regime
 */
export const validateOldRegimeDeductions = (deductions: any): { [key: string]: string[] } => {
  const errors: { [key: string]: string[] } = {};

  // 80C, 80CCC, 80CCD(1) combined limit
  const section80CCombined = (deductions.section80C || 0) + 
                             (deductions.section80CCC || 0) + 
                             (deductions.section80CCDEmployeeOrSE || 0);
  
  if (section80CCombined > 150000) {
    errors['combined80C'] = ['Combined deduction u/s 80C, 80CCC & 80CCD(1) cannot exceed Rs 1,50,000'];
  }

  // Individual limits
  if (deductions.section80CCD1B > 50000) {
    errors['section80CCD1B'] = ['Deduction u/s 80CCD(1B) cannot exceed Rs 50,000'];
  }

  if (deductions.section80D > 100000) {
    errors['section80D'] = ['Deduction u/s 80D cannot exceed Rs 1,00,000'];
  }

  if (deductions.section80TTA > 10000) {
    errors['section80TTA'] = ['Deduction u/s 80TTA cannot exceed Rs 10,000'];
  }

  return errors;
};

/**
 * Compare tax liability between old and new regime
 */
export const compareTaxRegimes = (totalIncome: number): {
  newRegime: TaxCalculations;
  oldRegime: TaxCalculations;
  recommendation: string;
} => {
  const newRegime = calculateNewRegimeTax(totalIncome);
  const oldRegime = calculateOldRegimeTax(totalIncome);

  const newRegimeTax = newRegime.netTaxLiability || 0;
  const oldRegimeTax = oldRegime.netTaxLiability || 0;

  let recommendation = '';
  if (newRegimeTax < oldRegimeTax) {
    recommendation = 'New tax regime is beneficial';
  } else if (oldRegimeTax < newRegimeTax) {
    recommendation = 'Old tax regime is beneficial';
  } else {
    recommendation = 'Both regimes have same tax liability';
  }

  return {
    newRegime,
    oldRegime,
    recommendation
  };
};

/**
 * Format currency for display
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

/**
 * Format number with Indian numbering system
 */
export const formatIndianNumber = (num: number): string => {
  return new Intl.NumberFormat('en-IN').format(num);
};

/**
 * Calculate refund or balance tax payable
 */
export const calculateRefundOrBalance = (
  totalTaxLiability: number,
  totalTaxesPaid: number
): { refundDue: number; balTaxPayable: number } => {
  const difference = totalTaxesPaid - totalTaxLiability;
  
  if (difference > 0) {
    return {
      refundDue: Math.round(difference),
      balTaxPayable: 0
    };
  } else {
    return {
      refundDue: 0,
      balTaxPayable: Math.round(Math.abs(difference))
    };
  }
};
